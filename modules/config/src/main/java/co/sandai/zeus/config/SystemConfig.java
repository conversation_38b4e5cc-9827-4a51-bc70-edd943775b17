package co.sandai.zeus.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class SystemConfig {
    @Value("${zeus.product-name:magi}")
    private String productName;

    @Value("${zeus.auth.disable-email-login:false}")
    private boolean disableEmailLogin;

    @Value("${zeus.auth.cookie.expire-in}")
    private int expireIn;

    @Value("${zeus.auth.cookie.name}")
    private String cookieName;

    @Value("${zeus.auth.cookie.http-only}")
    private boolean httpOnly;
}
