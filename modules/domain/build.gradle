plugins {
    id 'java'
}


tasks.named('test') {
    useJUnitPlatform()
}

configurations.all {
    exclude group: 'ch.qos.logback'
    exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
    exclude group: 'log4j', module: 'log4j' // 排除 Log4j 1.x
    exclude group: 'commons-logging', module: 'commons-logging'
}

dependencies {
    implementation project(':modules:config')
    implementation project(':modules:common')
    implementation project(':modules:infra')
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // Add Lombok explicitly
    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'

    // 中间件层
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'
    implementation 'com.github.yitter:yitter-idgenerator:1.0.6'
    implementation 'software.amazon.awssdk:s3:2.28.21'
    implementation 'redis.clients:jedis:5.2.0'
    implementation 'org.redisson:redisson-spring-boot-starter:3.40.0'

    // 各种工具
    implementation 'com.alibaba:fastjson:1.2.79'
    implementation 'com.alibaba.fastjson2:fastjson2:2.0.51'
    implementation 'com.alibaba.fastjson2:fastjson2-extension:2.0.51'
    implementation 'org.apache.commons:commons-lang3:3.17.0'

    // Jackson
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.17.1'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1'

    // 外部依赖
    implementation 'com.stripe:stripe-java:28.0.0'


    runtimeOnly 'com.mysql:mysql-connector-j'

    testImplementation project(':modules:api')

    testImplementation 'com.h2database:h2'
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.github.benas:random-beans:3.7.0'
}