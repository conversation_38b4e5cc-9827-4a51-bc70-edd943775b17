package co.sandai.zeus.api.system;

import co.sandai.zeus.api.system.view.SystemConfigView;
import co.sandai.zeus.config.SystemConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/system")
public class SystemController {

    @Autowired
    private SystemConfig systemConfig;

    @GetMapping("config")
    public SystemConfigView getSystemConfig() {
        return SystemConfigView.builder()
                .disableEmailLogin(systemConfig.isDisableEmailLogin())
                .build();
    }
}
