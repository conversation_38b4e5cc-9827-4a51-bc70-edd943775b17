package co.sandai.zeus.config;

import co.sandai.zeus.common.constant.ErrorCode;
import co.sandai.zeus.common.exception.RedirectException;
import co.sandai.zeus.common.exception.ZeusServiceException;
import co.sandai.zeus.common.vo.ErrorResponse;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.impl.FileSizeLimitExceededException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

@Slf4j
@RestControllerAdvice
public class ServiceExceptionHandler {

    @ExceptionHandler(value = ZeusServiceException.class)
    public @ResponseBody ResponseEntity<Object> handleServiceException(ZeusServiceException ex) {
        ErrorResponse body = ErrorResponse.builder()
                .code(ex.getCode())
                .message(ex.getMessage())
                .build();
        return ResponseEntity.status(ex.httpStatus)
                .contentType(org.springframework.http.MediaType.APPLICATION_JSON)
                .body(body);
    }

    @ExceptionHandler(value = RedirectException.class)
    public @ResponseBody ResponseEntity<Object> handleServiceException(RedirectException ex) {
        return ResponseEntity.status(HttpStatus.SEE_OTHER)
                .header("Location", ex.getRedirectUrl())
                .build();
    }

    /**
     * 这里如果需要返回英文的错误信息，request的header中需要包含Accept-Language: en。 或者至少不能带上Accept-Language: zh-CN
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public @ResponseBody ResponseEntity<Object> handleValidationException(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult()
                .getFieldErrors()
                .forEach(error -> errors.put(error.getField(), error.getDefaultMessage()));

        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InvalidParameters)
                .details(errors)
                .message("invalid parameters")
                .build();

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    /**
     * 处理方法参数验证异常（如@Pattern, @Valid等注解在@RequestParam, @PathVariable上的验证失败）
     */
    @ExceptionHandler(HandlerMethodValidationException.class)
    public @ResponseBody ResponseEntity<Object> handleHandlerMethodValidationException(
            HandlerMethodValidationException ex) {
        Map<String, String> errors = new HashMap<>();

        ex.getAllValidationResults().forEach(validationResult -> {
            String parameterName = validationResult.getMethodParameter().getParameterName();
            validationResult.getResolvableErrors().forEach(error -> {
                if (parameterName != null) {
                    errors.put(parameterName, error.getDefaultMessage());
                } else {
                    errors.put("parameter", error.getDefaultMessage());
                }
            });
        });

        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InvalidParameters)
                .details(errors)
                .message("invalid parameters")
                .build();

        log.warn("Handler method validation failed: {}", ex.getMessage());
        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class, FileSizeLimitExceededException.class})
    public @ResponseBody ResponseEntity<Object> handleMaxUploadSizeExceedException(Exception ex) {
        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.AssetTooLarge)
                .message("File size cannot be larger than 10MB")
                .build();

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public @ResponseBody ResponseEntity<Object> handleValidationException(NoResourceFoundException ex) {
        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.NotFound)
                .message(ex.getMessage())
                .build();

        return new ResponseEntity<>(body, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public @ResponseBody ResponseEntity<Object> handleMissingParameter(MissingServletRequestParameterException ex) {
        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InvalidParameters)
                .message("Missing required parameter: " + ex.getParameterName())
                .build();

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public @ResponseBody ResponseEntity<Object> handleBadRequest(HttpRequestMethodNotSupportedException ex) {
        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InvalidRequest)
                .message(ex.getMessage())
                .build();

        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public @ResponseBody ResponseEntity<Object> handleTypeMismatch(MethodArgumentTypeMismatchException ex) {
        Map<String, String> errors = new HashMap<>();
        String paramName = ex.getName();
        Object valueObj = ex.getValue();
        String value = (valueObj != null) ? valueObj.toString() : "null";
        Class<?> requiredType = ex.getRequiredType();
        String typeName = (requiredType != null) ? requiredType.getSimpleName() : "unknown";

        errors.put(paramName, String.format("Value '%s' is not a valid %s", value, typeName));

        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InvalidParameters)
                .details(errors)
                .message(String.format("Parameter '%s' has invalid value: %s", paramName, value))
                .build();

        log.warn("Parameter type mismatch: {} - expected {} but got {}", paramName, typeName, value);
        return new ResponseEntity<>(body, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(Throwable.class)
    public @ResponseBody ResponseEntity<Object> handleCommonException(Throwable ex) {
        log.error("Internal server error", ex);
        Map<String, String> errors = new HashMap<>();
        ErrorResponse body = ErrorResponse.builder()
                .code(ErrorCode.InternalError)
                .details(errors)
                .message("internal server error, please try again")
                .build();
        return new ResponseEntity<>(body, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
