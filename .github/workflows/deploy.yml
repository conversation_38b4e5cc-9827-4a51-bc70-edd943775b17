name: 镜像部署

on:
  workflow_dispatch:
    inputs:
      tag:
        type: string
        description: 镜像版本(Tag)
      env:
        description: "部署环境"
        required: true
        type: choice
        default: dev
        options:
          - dev
          - staging
          - prod
      deploy_zeus:
        description: '部署 Zeus'
        type: boolean
        default: true
      deploy_zeus_gaga:
        description: '部署 Zeus GaGa'
        type: boolean
        default: true
      deploy_platform:
        description: '部署 Platform'
        type: boolean
        default: true
      deploy_openapi:
        description: '部署 OpenAPI'
        type: boolean
        default: false

jobs:
  deploy:
    uses: ./.github/workflows/base-deployment.yml
    with:
      env: ${{inputs.env}}
      deploy_zeus: ${{inputs.deploy_zeus}}
      deploy_openapi: ${{inputs.deploy_openapi}}
      deploy_platform: ${{inputs.deploy_platform}}
      cluster_id: ${{ inputs.env == 'prod' && 'c7c0ede6c71484f8da34a829954c50cd9' || 'c4d615674e9994747af45004405e35503' }}
      envUrl: https://dev.sandaii.cn
      image_tag: ${{ inputs.tag }}
    secrets:
      aliyun_ak: "${{ secrets.ALIYUN_ACCESS_KEY_ID }}"
      aliyun_sk: "${{ secrets.ALIYUN_ACCESS_KEY_SECRET }}"
      feishu_webhook_url: "${{ secrets.FEISHU_WEBHOOK_URL }}"
